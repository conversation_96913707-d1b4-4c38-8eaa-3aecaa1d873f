{
  "folders": [
    {
      "path": "."
    }
  ],
  "settings": {
    // GitHub Copilot Configuration
    "github.copilot.enable": {
      "*": true,
      "yaml": true,
      "plaintext": true,
      "markdown": true,
      "json": true,
      "jsonc": true,
      "typescript": true,
      "javascript": true,
      "typescriptreact": true,
      "javascriptreact": true,
      "git-commit": true
    },
    
    // Enhanced Copilot Chat for Commit Messages
    "github.copilot.chat.enable": true,
    "github.copilot.chat.welcomeMessage": "never",
    "github.copilot.chat.commitMessageGeneration": "enabled",
    "github.copilot.chat.commitMessageGenerationMode": "enhanced",
    
    // Git Configuration
    "git.inputValidation": "always",
    "git.inputValidationLength": 72,
    "git.inputValidationSubjectLength": 50,
    "git.useCommitInputAsStashMessage": true,
    "git.verboseCommit": true,
    "git.enableSmartCommit": true,
    "git.confirmSync": false,
    "git.autofetch": true,
    
    // Gitmoji Extension Settings
    "gitmoji.showEmojiCode": true,
    "gitmoji.addCustomEmoji": true,
    "gitmoji.outputType": "emoji",
    "gitmoji.onlyUseCustomEmoji": false,
    
    // Editor Configuration for Commit Messages
    "editor.rulers": [50, 72],
    "editor.wordWrap": "wordWrapColumn",
    "editor.wordWrapColumn": 72,
    
    // Git Commit File Association
    "[git-commit]": {
      "editor.rulers": [50, 72],
      "editor.wordWrap": "wordWrapColumn",
      "editor.wordWrapColumn": 72,
      "editor.quickSuggestions": {
        "comments": true,
        "strings": true,
        "other": true
      },
      "editor.suggest.showSnippets": true,
      "editor.acceptSuggestionOnEnter": "on"
    },
    
    // Copilot Advanced Settings
    "github.copilot.advanced": {
      "length": 500,
      "temperature": 0.1,
      "top_p": 1,
      "stops": {
        "*": ["\n\n"]
      }
    },
    
    // Project Specific Settings
    "typescript.preferences.quoteStyle": "single",
    "javascript.preferences.quoteStyle": "single",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    },
    
    // File Associations
    "files.associations": {
      "COMMIT_EDITMSG": "git-commit",
      ".gitmessage": "git-commit"
    }
  },
  "extensions": {
    "recommendations": [
      "github.copilot",
      "github.copilot-chat",
      "seatonjiang.gitmoji-vscode",
      "vivaxy.vscode-conventional-commits",
      "ms-vscode.vscode-json"
    ]
  }
}
