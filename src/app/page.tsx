'use client';

export default function Home() {
  return (
    <div className='max-w-7xl mx-auto'>
      <div className='text-center space-y-4'>
        <h1 className='text-4xl font-bold text-gradient'>
          Welcome to PromptRank
        </h1>
        <p className='text-xl text-muted-foreground max-w-2xl mx-auto'>
          Discover, rate, and share the best AI prompts. The Header component
          has been refactored to use Next.js navigation with proper TypeScript
          interfaces, custom hooks, mobile responsiveness, and theme management.
        </p>
        <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mt-12'>
          <div className='glass p-6 rounded-lg theme-transition'>
            <h3 className='text-lg font-semibold mb-2'>🏠 Home</h3>
            <p className='text-muted-foreground'>
              Browse and discover trending prompts
            </p>
          </div>
          <div className='glass p-6 rounded-lg theme-transition'>
            <h3 className='text-lg font-semibold mb-2'>📤 Upload</h3>
            <p className='text-muted-foreground'>
              Share your best prompts with the community
            </p>
          </div>
          <div className='glass p-6 rounded-lg theme-transition'>
            <h3 className='text-lg font-semibold mb-2'>👤 Profile</h3>
            <p className='text-muted-foreground'>
              Manage your account and view your contributions
            </p>
          </div>
        </div>
      </div>
      <div className='text-center space-y-4'>
        <h1 className='text-4xl font-bold text-gradient'>
          Welcome to PromptRank
        </h1>
        <p className='text-xl text-muted-foreground max-w-2xl mx-auto'>
          Discover, rate, and share the best AI prompts. The Header component
          has been refactored to use Next.js navigation with proper TypeScript
          interfaces, custom hooks, mobile responsiveness, and theme management.
        </p>
        <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mt-12'>
          <div className='glass p-6 rounded-lg theme-transition'>
            <h3 className='text-lg font-semibold mb-2'>🏠 Home</h3>
            <p className='text-muted-foreground'>
              Browse and discover trending prompts
            </p>
          </div>
          <div className='glass p-6 rounded-lg theme-transition'>
            <h3 className='text-lg font-semibold mb-2'>📤 Upload</h3>
            <p className='text-muted-foreground'>
              Share your best prompts with the community
            </p>
          </div>
          <div className='glass p-6 rounded-lg theme-transition'>
            <h3 className='text-lg font-semibold mb-2'>👤 Profile</h3>
            <p className='text-muted-foreground'>
              Manage your account and view your contributions
            </p>
          </div>
        </div>
      </div>
      <div className='text-center space-y-4'>
        <h1 className='text-4xl font-bold text-gradient'>
          Welcome to PromptRank
        </h1>
        <p className='text-xl text-muted-foreground max-w-2xl mx-auto'>
          Discover, rate, and share the best AI prompts. The Header component
          has been refactored to use Next.js navigation with proper TypeScript
          interfaces, custom hooks, mobile responsiveness, and theme management.
        </p>
        <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mt-12'>
          <div className='glass p-6 rounded-lg theme-transition'>
            <h3 className='text-lg font-semibold mb-2'>🏠 Home</h3>
            <p className='text-muted-foreground'>
              Browse and discover trending prompts
            </p>
          </div>
          <div className='glass p-6 rounded-lg theme-transition'>
            <h3 className='text-lg font-semibold mb-2'>📤 Upload</h3>
            <p className='text-muted-foreground'>
              Share your best prompts with the community
            </p>
          </div>
          <div className='glass p-6 rounded-lg theme-transition'>
            <h3 className='text-lg font-semibold mb-2'>👤 Profile</h3>
            <p className='text-muted-foreground'>
              Manage your account and view your contributions
            </p>
          </div>
        </div>
      </div>
      <div className='text-center space-y-4'>
        <h1 className='text-4xl font-bold text-gradient'>
          Welcome to PromptRank
        </h1>
        <p className='text-xl text-muted-foreground max-w-2xl mx-auto'>
          Discover, rate, and share the best AI prompts. The Header component
          has been refactored to use Next.js navigation with proper TypeScript
          interfaces, custom hooks, mobile responsiveness, and theme management.
        </p>
        <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mt-12'>
          <div className='glass p-6 rounded-lg theme-transition'>
            <h3 className='text-lg font-semibold mb-2'>🏠 Home</h3>
            <p className='text-muted-foreground'>
              Browse and discover trending prompts
            </p>
          </div>
          <div className='glass p-6 rounded-lg theme-transition'>
            <h3 className='text-lg font-semibold mb-2'>📤 Upload</h3>
            <p className='text-muted-foreground'>
              Share your best prompts with the community
            </p>
          </div>
          <div className='glass p-6 rounded-lg theme-transition'>
            <h3 className='text-lg font-semibold mb-2'>👤 Profile</h3>
            <p className='text-muted-foreground'>
              Manage your account and view your contributions
            </p>
          </div>
        </div>
      </div>
      <div className='text-center space-y-4'>
        <h1 className='text-4xl font-bold text-gradient'>
          Welcome to PromptRank
        </h1>
        <p className='text-xl text-muted-foreground max-w-2xl mx-auto'>
          Discover, rate, and share the best AI prompts. The Header component
          has been refactored to use Next.js navigation with proper TypeScript
          interfaces, custom hooks, mobile responsiveness, and theme management.
        </p>
        <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mt-12'>
          <div className='glass p-6 rounded-lg theme-transition'>
            <h3 className='text-lg font-semibold mb-2'>🏠 Home</h3>
            <p className='text-muted-foreground'>
              Browse and discover trending prompts
            </p>
          </div>
          <div className='glass p-6 rounded-lg theme-transition'>
            <h3 className='text-lg font-semibold mb-2'>📤 Upload</h3>
            <p className='text-muted-foreground'>
              Share your best prompts with the community
            </p>
          </div>
          <div className='glass p-6 rounded-lg theme-transition'>
            <h3 className='text-lg font-semibold mb-2'>👤 Profile</h3>
            <p className='text-muted-foreground'>
              Manage your account and view your contributions
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
