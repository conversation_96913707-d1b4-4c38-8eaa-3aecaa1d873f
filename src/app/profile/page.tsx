'use client';

import { Header } from '@/components';

export default function ProfilePage() {
  return (
    <div className='min-h-screen bg-background'>
      <Header />
      <main className='pt-20 p-6'>
        <div className='max-w-7xl mx-auto'>
          <div className='text-center space-y-4'>
            <h1 className='text-4xl font-bold text-gradient'>Profile</h1>
            <p className='text-xl text-muted-foreground max-w-2xl mx-auto'>
              Manage your account, view your contributions, and track your
              prompt rankings and community engagement.
            </p>
            <div className='glass p-8 rounded-lg theme-transition max-w-2xl mx-auto mt-12'>
              <h3 className='text-lg font-semibold mb-4'>👤 Your Profile</h3>
              <p className='text-muted-foreground'>
                Profile management interface would go here. This demonstrates
                the navigation working with Next.js routing.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
